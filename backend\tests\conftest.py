# backend/tests/conftest.py
"""
Pytest configuration and shared fixtures for the test suite.

This module provides common test fixtures and configuration that can be
used across all test modules.
"""

import os
import sys

import pytest
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.models.enums import InstallationEnvironment
from core.models.project import Project

# Import only the models we need for testing to avoid dependency issues
# from backend.core.models import *  # Don't import all models


@pytest.fixture(scope="session")
def test_engine():
    """Create an in-memory SQLite database engine for testing."""
    engine = create_engine(
        "sqlite:///:memory:",
        connect_args={"check_same_thread": False},
        poolclass=StaticPool,
        echo=False,  # Set to True for SQL debugging
    )

    # Create only the Project table to avoid dependency issues
    Project.__table__.create(engine, checkfirst=True)

    return engine


@pytest.fixture(scope="function")
def db_session(test_engine):
    """Create a database session for testing with automatic rollback."""
    connection = test_engine.connect()
    transaction = connection.begin()

    # Create session bound to the connection
    Session = sessionmaker(bind=connection)
    session = Session()

    yield session

    # Rollback transaction and close connection
    session.close()
    transaction.rollback()
    connection.close()


@pytest.fixture
def sample_project_data():
    """Sample project data for testing."""
    return {
        "name": "Test Heat Tracing Project",
        "project_number": "HT-TEST-001",
        "description": "A test project for heat tracing design",
        "designer": "Test Engineer",
        "notes": "Test project notes",
        "min_ambient_temp_c": -20.0,
        "max_ambient_temp_c": 40.0,
        "desired_maintenance_temp_c": 65.0,
        "wind_speed_ms": 5.0,
        "installation_environment": InstallationEnvironment.OUTDOOR,
        "available_voltages_json": "[120, 240, 480]",
        "default_cable_manufacturer": "Test Manufacturer",
        "default_control_device_manufacturer": "Test Control Manufacturer",
    }


@pytest.fixture
def sample_project_create_data():
    """Sample project creation data (without auto-generated fields)."""
    return {
        "name": "Test Heat Tracing Project",
        "project_number": "HT-TEST-001",
        "description": "A test project for heat tracing design",
        "designer": "Test Engineer",
        "notes": "Test project notes",
        "min_ambient_temp_c": -20.0,
        "max_ambient_temp_c": 40.0,
        "desired_maintenance_temp_c": 65.0,
        "wind_speed_ms": 5.0,
        "installation_environment": InstallationEnvironment.OUTDOOR,
        "available_voltages_json": "[120, 240, 480]",
        "default_cable_manufacturer": "Test Manufacturer",
        "default_control_device_manufacturer": "Test Control Manufacturer",
    }


@pytest.fixture
def sample_project_update_data():
    """Sample project update data."""
    return {
        "name": "Updated Test Project",
        "description": "Updated description",
        "max_ambient_temp_c": 45.0,
        "default_cable_manufacturer": "Updated Manufacturer",
    }


@pytest.fixture
def sample_project_orm(db_session, sample_project_data):
    """Create a sample Project ORM instance in the database."""
    project = Project(**sample_project_data)
    db_session.add(project)
    db_session.commit()
    db_session.refresh(project)
    return project


@pytest.fixture
def multiple_projects_orm(db_session):
    """Create multiple Project ORM instances for testing pagination."""
    projects = []
    for i in range(15):
        project_data = {
            "name": f"Test Project {i + 1}",
            "project_number": f"HT-TEST-{i + 1:03d}",
            "description": f"Test project {i + 1} description",
            "designer": f"Engineer {i + 1}",
            "min_ambient_temp_c": -20.0,
            "max_ambient_temp_c": 40.0,
            "desired_maintenance_temp_c": 65.0,
            "wind_speed_ms": 5.0,
            "installation_environment": InstallationEnvironment.OUTDOOR
            if i % 2 == 0
            else InstallationEnvironment.INDOOR,
        }
        project = Project(**project_data)
        db_session.add(project)
        projects.append(project)

    db_session.commit()

    # Refresh all projects to get their IDs
    for project in projects:
        db_session.refresh(project)

    return projects


@pytest.fixture
def invalid_project_data():
    """Invalid project data for testing validation."""
    return {
        "name": "",  # Invalid: empty name
        "project_number": "invalid chars!@#",  # Invalid: special characters
        "min_ambient_temp_c": 50.0,  # Invalid: min > max
        "max_ambient_temp_c": 40.0,
        "desired_maintenance_temp_c": -10.0,  # Invalid: negative maintenance temp
        "wind_speed_ms": -5.0,  # Invalid: negative wind speed
        "available_voltages_json": "invalid json",  # Invalid: malformed JSON
    }


# Test utilities
class TestUtils:
    """Utility functions for testing."""

    @staticmethod
    def assert_project_fields_equal(project1, project2, exclude_fields=None):
        """Assert that two projects have equal field values."""
        exclude_fields = exclude_fields or ["id", "created_at", "updated_at"]

        for field in project1.__table__.columns.keys():
            if field not in exclude_fields:
                assert getattr(project1, field) == getattr(project2, field), (
                    f"Field {field} differs"
                )

    @staticmethod
    def create_test_project(session, **overrides):
        """Create a test project with optional field overrides."""
        default_data = {
            "name": "Test Project",
            "project_number": "TEST-001",
            "min_ambient_temp_c": -20.0,
            "max_ambient_temp_c": 40.0,
            "desired_maintenance_temp_c": 65.0,
        }
        default_data.update(overrides)

        project = Project(**default_data)
        session.add(project)
        session.commit()
        session.refresh(project)
        return project


@pytest.fixture
def test_utils():
    """Provide test utilities."""
    return TestUtils
