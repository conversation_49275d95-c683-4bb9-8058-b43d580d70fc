# backend/core/repositories/base_repository.py
from typing import Any, Dict, Generic, List, Optional, Type, TypeVar

from sqlalchemy import select
from sqlalchemy.exc import IntegrityError, NoResultFound, SQLAlchemyError
from sqlalchemy.orm import Session

try:
    from backend.core.errors.exceptions import (
        DatabaseError,
        DuplicateEntryError,
        NotFoundError,
    )
except ImportError:
    from core.errors.exceptions import (
        DatabaseError,
        DuplicateEntryError,
        NotFoundError,
    )
try:
    from backend.core.models.base import Base
except ImportError:
    from core.models.base import Base

# Define a generic type for SQLAlchemy models
ModelType = TypeVar("ModelType", bound=Base)


class BaseRepository(Generic[ModelType]):
    def __init__(self, db_session: Session, model: Type[ModelType]):
        self.db_session = db_session
        self.model = model

    def _handle_db_exception(
        self, e: SQLAlchemyError, entity_name: str = "resource"
    ) -> None:
        if isinstance(e, IntegrityError):
            raise DuplicateEntryError(
                message=f"A {entity_name} with the given unique constraint already exists.",
                original_exception=e,
            )
        elif isinstance(e, NoResultFound):
            raise NotFoundError(
                code=f"{entity_name.upper()}_NOT_FOUND",
                detail=f"{entity_name.capitalize()} not found.",
            )
        else:
            raise DatabaseError(
                reason=f"An unexpected database error occurred during {entity_name} operation.",
                original_exception=e,
            ) from e

    def get_by_id(self, item_id: int) -> Optional[ModelType]:
        try:
            stmt = select(self.model).where(self.model.id == item_id)
            return self.db_session.scalar(stmt)
        except SQLAlchemyError as e:
            self._handle_db_exception(e, self.model.__tablename__)
            return None  # This line will never be reached due to exception, but satisfies type checker

    def get_all(self, skip: int = 0, limit: int = 100) -> List[ModelType]:
        try:
            stmt = select(self.model).offset(skip).limit(limit)
            return list(self.db_session.scalars(stmt).all())
        except SQLAlchemyError as e:
            self._handle_db_exception(e, self.model.__tablename__)
            return []  # This line will never be reached due to exception, but satisfies type checker

    def create(self, data: Dict[str, Any]) -> ModelType:
        try:
            item = self.model(**data)
            self.db_session.add(item)
            # Note: db_session.commit() is typically done by the service layer,
            # but for simplicity, we might do it here if it's a single operation
            # self.db_session.commit()
            # self.db_session.refresh(item)
            return item
        except SQLAlchemyError as e:
            self._handle_db_exception(e, self.model.__tablename__)
            raise  # This line will never be reached due to exception, but satisfies type checker

    # ... other common CRUD methods like update, delete, filter, etc.
