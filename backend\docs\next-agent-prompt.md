# Next AI Agent Implementation Prompt

## 🎯 **Mission: Implement Heat Tracing Entity**

You are continuing the implementation of the Ultimate Electrical Designer backend. The foundation is solid and ready for the next major entity implementation.

## 📋 **Current State Summary**

### ✅ **Completed Foundation (100%)**
1. **Project Entity** - Complete with all 5 layers + comprehensive tests
2. **Component Entity** - Complete with catalog management and validation  
3. **Calculations Layer** - Engineering calculations (heat loss, electrical sizing)
4. **Standards Layer** - TR 50410, IEC 60079-30-1 compliance validation
5. **Model-Level Validation** - SQLAlchemy event listeners for data integrity

### 🔧 **Established Architecture Patterns**
- **5-Layer Architecture**: Model → Repository → Service → API → Tests
- **Comprehensive Error Handling**: Custom exceptions with detailed error context
- **Standards Integration**: Calculations integrate with compliance validation
- **Robust Testing**: Unit tests for each layer with mocking strategies
- **Type Safety**: Full type hints and Pydantic validation throughout

## 🎯 **Your Task: Heat Tracing Entity Implementation**

### **Priority**: CRITICAL - Core Business Domain
The Heat Tracing entity represents the primary engineering workflow and integrates heavily with the calculations and standards layers.

### **Models to Implement** (Already exist in `backend/core/models/heat_tracing.py`):
1. **Pipe** - Heat traced pipes with thermal calculations
2. **Vessel** - Heat traced vessels and tanks  
3. **HTCircuit** - Heat tracing circuits with power calculations
4. **HTCircuitPipe** - Association between circuits and pipes
5. **HTCircuitVessel** - Association between circuits and vessels

### **Required Implementation (Follow Established Patterns)**

#### 1. **Heat Tracing Schemas** (`backend/core/schemas/heat_tracing_schemas.py`)
**Create comprehensive Pydantic schemas for all heat tracing entities:**

- **PipeCreateSchema/UpdateSchema/ReadSchema** - Pipe management with thermal properties
- **VesselCreateSchema/UpdateSchema/ReadSchema** - Vessel management with geometry validation
- **HTCircuitCreateSchema/UpdateSchema/ReadSchema** - Circuit design with power calculations
- **Calculation Input/Output Schemas** - Integration with calculations layer
- **Validation Rules** - Engineering constraints and business logic

**Key Requirements:**
- Integrate with calculations layer for heat loss validation
- Validate engineering constraints (temperatures, dimensions, materials)
- Support complex nested relationships (circuits → pipes/vessels)
- Include calculation result schemas for API responses

#### 2. **Heat Tracing Repositories** (`backend/core/repositories/heat_tracing_repository.py`)
**Extend BaseRepository for each heat tracing model:**

- **PipeRepository** - CRUD + project-scoped queries + thermal property filtering
- **VesselRepository** - CRUD + geometry-based queries + heat loss calculations
- **HTCircuitRepository** - CRUD + circuit design queries + power calculations
- **Complex Queries** - Circuit assignment, load balancing, optimization queries
- **Performance Optimization** - Eager loading for related calculations

#### 3. **Heat Tracing Service** (`backend/core/services/heat_tracing_service.py`)
**Business logic layer with engineering calculations integration:**

- **Design Workflow** - Complete heat tracing design process
- **Calculations Integration** - Use `CalculationService` for heat loss and power calculations
- **Standards Validation** - Use `StandardsManager` for compliance checking
- **Circuit Assignment** - Automatic circuit assignment algorithms
- **Optimization Logic** - Power balancing and efficiency optimization
- **Validation** - Engineering constraint validation and safety checks

**Critical Integration Points:**
```python
# Example integration pattern
from backend.core.calculations.calculation_service import CalculationService, HeatLossInput
from backend.core.standards.standards_manager import StandardsManager

# In service methods:
heat_loss_result = self.calculation_service.calculate_heat_loss(heat_loss_input)
validation_result = self.standards_manager.validate_heat_loss_calculation(heat_loss_result, design_params)
```

#### 4. **Heat Tracing API Routes** (`backend/api/v1/heat_tracing_routes.py`)
**RESTful endpoints for heat tracing operations:**

- **CRUD Endpoints** - Standard create/read/update/delete for all entities
- **Design Endpoints** - Circuit design and optimization endpoints
- **Calculation Endpoints** - Heat loss and power calculation endpoints
- **Validation Endpoints** - Standards compliance checking endpoints
- **Complex Operations** - Bulk operations, circuit assignment, optimization

#### 5. **Comprehensive Test Suite**
**Follow established testing patterns:**

- **Schema Tests** - Validation rules, calculation integration, nested relationships
- **Repository Tests** - CRUD operations, complex queries, performance optimization
- **Service Tests** - Business logic, calculations integration, standards validation
- **API Tests** - All endpoints, error scenarios, integration workflows
- **Integration Tests** - End-to-end heat tracing design workflows

## 🔗 **Critical Integration Requirements**

### **Calculations Layer Integration**
- Use `CalculationService` for all thermal calculations
- Integrate heat loss results into pipe/vessel entities
- Apply calculation results to circuit sizing and power requirements
- Handle calculation errors gracefully with proper user feedback

### **Standards Layer Integration**  
- Use `StandardsManager` for all compliance validation
- Apply safety factors from active standards (TR 50410, IEC 60079-30-1)
- Validate temperature classes for hazardous area applications
- Ensure all designs meet engineering standards requirements

### **Component Integration**
- Reference heating cables from Component entity
- Validate component compatibility with design requirements
- Use component specifications in calculations and validation

## 📚 **Reference Implementation Patterns**

### **Study These Completed Examples:**
1. **Project Entity** - `backend/core/schemas/project_schemas.py` for schema patterns
2. **Component Entity** - `backend/core/services/component_service.py` for service patterns  
3. **Calculation Service** - `backend/core/calculations/calculation_service.py` for integration
4. **Standards Manager** - `backend/core/standards/standards_manager.py` for validation

### **Follow These Conventions:**
- Use `BaseSoftDeleteSchema` for read schemas with soft delete fields
- Implement comprehensive logging with contextual information
- Use custom exceptions from `backend.core.errors.exceptions`
- Follow established error handling and transaction management patterns
- Maintain high test coverage (>90%) with proper mocking strategies

## 🎯 **Success Criteria**

### **Functional Requirements:**
- [ ] Complete heat tracing design workflow from pipe/vessel input to circuit assignment
- [ ] Integration with calculations layer for accurate heat loss and power calculations
- [ ] Standards compliance validation for all designs
- [ ] Comprehensive error handling and user feedback
- [ ] High-performance queries for complex heat tracing operations

### **Quality Requirements:**
- [ ] >90% test coverage across all layers
- [ ] Comprehensive type hints and documentation
- [ ] Proper error handling with detailed error messages
- [ ] Performance optimization for complex calculations
- [ ] Integration tests demonstrating end-to-end workflows

### **Integration Requirements:**
- [ ] Seamless integration with calculations layer
- [ ] Standards validation for all heat tracing designs
- [ ] Component compatibility validation
- [ ] Project-scoped operations with proper access control

## 🚀 **Getting Started**

1. **Review the existing models** in `backend/core/models/heat_tracing.py`
2. **Study the calculation examples** - Run the working heat loss calculation
3. **Examine the standards validation** - Test the working standards manager
4. **Follow the established patterns** from Project and Component entities
5. **Start with schemas** - Build the foundation with comprehensive validation
6. **Integrate early and often** - Test calculations and standards integration continuously

## 📖 **Key Documentation References**

- `backend/docs/core/calculations/calculations-architecture.md` - Calculations integration guide
- `backend/docs/core/standards/standards-architecture.md` - Standards validation guide  
- `backend/docs/core/models/validation-architecture.md` - Model validation patterns
- `backend/docs/implementation-progress.md` - Current progress and patterns
- `backend/docs/project-entity-completion-summary.md` - Established conventions

**Remember**: You're building on a solid, tested foundation. The calculations and standards layers are working and ready for integration. Focus on creating a seamless heat tracing design experience that leverages all the architectural components we've built! 🎯
