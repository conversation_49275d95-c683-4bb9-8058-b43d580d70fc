# backend/core/repositories/heat_tracing_repository.py
"""
Heat Tracing Repository

This module provides data access layer for Heat Tracing entities, extending the base
repository with heat tracing-specific query methods and operations.
"""

from typing import Any, Dict, List, Optional

from sqlalchemy import and_, func, select, update
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session

from backend.config.logging_config import get_logger
from backend.core.models.heat_tracing import (
    ControlCircuit,
    HTCircuit,
    Pipe,
    Vessel,
)
from backend.core.repositories.base_repository import BaseRepository

# Initialize logger for this module
logger = get_logger(__name__)


class PipeRepository(BaseRepository[Pipe]):
    """
    Repository for Pipe entity data access operations.

    Extends BaseRepository with pipe-specific query methods and
    enhanced error handling for pipe operations.
    """

    def __init__(self, db_session: Session):
        """
        Initialize the Pipe repository.

        Args:
            db_session: SQLAlchemy database session
        """
        super().__init__(db_session, Pipe)
        logger.debug("PipeRepository initialized")

    def get_by_project_id(
        self, project_id: int, skip: int = 0, limit: int = 100
    ) -> List[Pipe]:
        """
        Get pipes by project ID.

        Args:
            project_id: Project ID to filter by
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return

        Returns:
            List[Pipe]: List of pipes in the project

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(
            f"Retrieving pipes for project {project_id}: skip={skip}, limit={limit}"
        )

        try:
            stmt = (
                select(self.model)
                .where(
                    and_(
                        self.model.project_id == project_id,
                        self.model.is_deleted == False,
                    )
                )
                .offset(skip)
                .limit(limit)
                .order_by(self.model.name)
            )
            results = list(self.db_session.scalars(stmt).all())

            logger.debug(f"Retrieved {len(results)} pipes for project {project_id}")
            return results

        except SQLAlchemyError as e:
            logger.error(
                f"Database error retrieving pipes for project {project_id}: {e}"
            )
            self._handle_db_exception(e, "pipe")
            raise  # This will never be reached due to _handle_db_exception raising

    def get_by_line_tag(self, project_id: int, line_tag: str) -> Optional[Pipe]:
        """
        Get pipe by line tag within a project.

        Args:
            project_id: Project ID
            line_tag: Line tag to search for

        Returns:
            Optional[Pipe]: The found pipe or None

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(
            f"Searching for pipe with line tag: {line_tag} in project {project_id}"
        )

        try:
            stmt = select(self.model).where(
                and_(
                    self.model.project_id == project_id,
                    self.model.line_tag == line_tag,
                    self.model.is_deleted == False,
                )
            )
            result = self.db_session.scalar(stmt)

            if result:
                logger.debug(f"Found pipe: '{result.name}' (ID: {result.id})")
            else:
                logger.debug(f"Pipe not found with line tag: {line_tag}")

            return result

        except SQLAlchemyError as e:
            logger.error(f"Database error searching for pipe line tag {line_tag}: {e}")
            self._handle_db_exception(e, "pipe")
            raise

    def get_pipes_without_circuits(self, project_id: int) -> List[Pipe]:
        """
        Get pipes that don't have heat tracing circuits assigned.

        Args:
            project_id: Project ID to filter by

        Returns:
            List[Pipe]: List of pipes without circuits

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Retrieving pipes without circuits for project {project_id}")

        try:
            stmt = (
                select(self.model)
                .where(
                    and_(
                        self.model.project_id == project_id,
                        self.model.is_deleted == False,
                        ~self.model.htcircuit.has(),  # No related HTCircuit
                    )
                )
                .order_by(self.model.name)
            )
            results = list(self.db_session.scalars(stmt).all())

            logger.debug(f"Found {len(results)} pipes without circuits")
            return results

        except SQLAlchemyError as e:
            logger.error(f"Database error retrieving pipes without circuits: {e}")
            self._handle_db_exception(e, "pipe")
            raise

    def get_with_heat_loss_calculations(self, project_id: int) -> List[Pipe]:
        """
        Get pipes with heat loss calculations.

        Args:
            project_id: Project ID to filter by

        Returns:
            List[Pipe]: List of pipes with heat loss calculations

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(
            f"Retrieving pipes with heat loss calculations for project {project_id}"
        )

        try:
            stmt = (
                select(self.model)
                .where(
                    and_(
                        self.model.project_id == project_id,
                        self.model.is_deleted == False,
                        self.model.calculated_heat_loss_wm.isnot(None),
                    )
                )
                .order_by(self.model.calculated_heat_loss_wm.desc())
            )
            results = list(self.db_session.scalars(stmt).all())

            logger.debug(f"Found {len(results)} pipes with heat loss calculations")
            return results

        except SQLAlchemyError as e:
            logger.error(f"Database error retrieving pipes with calculations: {e}")
            self._handle_db_exception(e, "pipe")
            raise

    def update_heat_loss_calculation(
        self, pipe_id: int, heat_loss_wm: float, required_output_wm: float
    ) -> Optional[Pipe]:
        """
        Update pipe heat loss calculation results.

        Args:
            pipe_id: ID of pipe to update
            heat_loss_wm: Calculated heat loss in W/m
            required_output_wm: Required heat output in W/m

        Returns:
            Optional[Pipe]: Updated pipe or None if not found

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Updating heat loss calculation for pipe {pipe_id}")

        try:
            # First check if pipe exists
            existing_pipe = self.get_by_id(pipe_id)
            if existing_pipe is None:
                logger.debug(f"Pipe {pipe_id} not found for heat loss update")
                return None

            # Update the pipe
            stmt = (
                update(self.model)
                .where(self.model.id == pipe_id)
                .values(
                    calculated_heat_loss_wm=heat_loss_wm,
                    required_heat_output_wm=required_output_wm,
                )
            )
            self.db_session.execute(stmt)

            # Return updated pipe
            updated_pipe = self.get_by_id(pipe_id)
            logger.debug(f"Pipe {pipe_id} heat loss calculation updated successfully")
            return updated_pipe

        except SQLAlchemyError as e:
            logger.error(
                f"Database error updating pipe heat loss calculation {pipe_id}: {e}"
            )
            self._handle_db_exception(e, "pipe")

    def count_by_project(self, project_id: int) -> int:
        """
        Count total number of active pipes in a project.

        Args:
            project_id: Project ID

        Returns:
            int: Number of active pipes

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Counting pipes for project {project_id}")

        try:
            stmt = select(func.count(self.model.id)).where(
                and_(
                    self.model.project_id == project_id, self.model.is_deleted == False
                )
            )
            result = self.db_session.scalar(stmt)

            logger.debug(f"Total pipes in project {project_id}: {result}")
            return result or 0

        except SQLAlchemyError as e:
            logger.error(f"Database error counting pipes for project {project_id}: {e}")
            self._handle_db_exception(e, "pipe")
            raise


class VesselRepository(BaseRepository[Vessel]):
    """
    Repository for Vessel entity data access operations.

    Extends BaseRepository with vessel-specific query methods and
    enhanced error handling for vessel operations.
    """

    def __init__(self, db_session: Session):
        """
        Initialize the Vessel repository.

        Args:
            db_session: SQLAlchemy database session
        """
        super().__init__(db_session, Vessel)
        logger.debug("VesselRepository initialized")

    def get_by_project_id(
        self, project_id: int, skip: int = 0, limit: int = 100
    ) -> List[Vessel]:
        """
        Get vessels by project ID.

        Args:
            project_id: Project ID to filter by
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return

        Returns:
            List[Vessel]: List of vessels in the project

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(
            f"Retrieving vessels for project {project_id}: skip={skip}, limit={limit}"
        )

        try:
            stmt = (
                select(self.model)
                .where(
                    and_(
                        self.model.project_id == project_id,
                        self.model.is_deleted == False,
                    )
                )
                .offset(skip)
                .limit(limit)
                .order_by(self.model.name)
            )
            results = list(self.db_session.scalars(stmt).all())

            logger.debug(f"Retrieved {len(results)} vessels for project {project_id}")
            return results

        except SQLAlchemyError as e:
            logger.error(
                f"Database error retrieving vessels for project {project_id}: {e}"
            )
            self._handle_db_exception(e, "vessel")
            raise

    def get_by_equipment_tag(
        self, project_id: int, equipment_tag: str
    ) -> Optional[Vessel]:
        """
        Get vessel by equipment tag within a project.

        Args:
            project_id: Project ID
            equipment_tag: Equipment tag to search for

        Returns:
            Optional[Vessel]: The found vessel or None

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(
            f"Searching for vessel with equipment tag: {equipment_tag} in project {project_id}"
        )

        try:
            stmt = select(self.model).where(
                and_(
                    self.model.project_id == project_id,
                    self.model.equipment_tag == equipment_tag,
                    self.model.is_deleted == False,
                )
            )
            result = self.db_session.scalar(stmt)

            if result:
                logger.debug(f"Found vessel: '{result.name}' (ID: {result.id})")
            else:
                logger.debug(f"Vessel not found with equipment tag: {equipment_tag}")

            return result

        except SQLAlchemyError as e:
            logger.error(
                f"Database error searching for vessel equipment tag {equipment_tag}: {e}"
            )
            self._handle_db_exception(e, "vessel")
            raise

    def get_vessels_without_circuits(self, project_id: int) -> List[Vessel]:
        """
        Get vessels that don't have heat tracing circuits assigned.

        Args:
            project_id: Project ID to filter by

        Returns:
            List[Vessel]: List of vessels without circuits

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Retrieving vessels without circuits for project {project_id}")

        try:
            stmt = (
                select(self.model)
                .where(
                    and_(
                        self.model.project_id == project_id,
                        self.model.is_deleted == False,
                        ~self.model.htcircuit.has(),  # No related HTCircuit
                    )
                )
                .order_by(self.model.name)
            )
            results = list(self.db_session.scalars(stmt).all())

            logger.debug(f"Found {len(results)} vessels without circuits")
            return results

        except SQLAlchemyError as e:
            logger.error(f"Database error retrieving vessels without circuits: {e}")
            self._handle_db_exception(e, "vessel")
            raise

    def update_heat_loss_calculation(
        self, vessel_id: int, heat_loss_w: float, required_output_w: float
    ) -> Optional[Vessel]:
        """
        Update vessel heat loss calculation results.

        Args:
            vessel_id: ID of vessel to update
            heat_loss_w: Calculated heat loss in W
            required_output_w: Required heat output in W

        Returns:
            Optional[Vessel]: Updated vessel or None if not found

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Updating heat loss calculation for vessel {vessel_id}")

        try:
            # First check if vessel exists
            existing_vessel = self.get_by_id(vessel_id)
            if existing_vessel is None:
                logger.debug(f"Vessel {vessel_id} not found for heat loss update")
                return None

            # Update the vessel
            stmt = (
                update(self.model)
                .where(self.model.id == vessel_id)
                .values(
                    calculated_heat_loss_w=heat_loss_w,
                    required_heat_output_w=required_output_w,
                )
            )
            self.db_session.execute(stmt)

            # Return updated vessel
            updated_vessel = self.get_by_id(vessel_id)
            logger.debug(
                f"Vessel {vessel_id} heat loss calculation updated successfully"
            )
            return updated_vessel

        except SQLAlchemyError as e:
            logger.error(
                f"Database error updating vessel heat loss calculation {vessel_id}: {e}"
            )
            self._handle_db_exception(e, "vessel")

    def count_by_project(self, project_id: int) -> int:
        """
        Count total number of active vessels in a project.

        Args:
            project_id: Project ID

        Returns:
            int: Number of active vessels

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Counting vessels for project {project_id}")

        try:
            stmt = select(func.count(self.model.id)).where(
                and_(
                    self.model.project_id == project_id, self.model.is_deleted == False
                )
            )
            result = self.db_session.scalar(stmt)

            logger.debug(f"Total vessels in project {project_id}: {result}")
            return result or 0

        except SQLAlchemyError as e:
            logger.error(
                f"Database error counting vessels for project {project_id}: {e}"
            )
            self._handle_db_exception(e, "vessel")
            raise


class HTCircuitRepository(BaseRepository[HTCircuit]):
    """
    Repository for HTCircuit entity data access operations.

    Extends BaseRepository with HT circuit-specific query methods and
    enhanced error handling for HT circuit operations.
    """

    def __init__(self, db_session: Session):
        """
        Initialize the HTCircuit repository.

        Args:
            db_session: SQLAlchemy database session
        """
        super().__init__(db_session, HTCircuit)
        logger.debug("HTCircuitRepository initialized")

    def get_by_feeder_id(
        self, feeder_id: int, skip: int = 0, limit: int = 100
    ) -> List[HTCircuit]:
        """
        Get HT circuits by feeder ID.

        Args:
            feeder_id: Feeder ID to filter by
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return

        Returns:
            List[HTCircuit]: List of HT circuits on the feeder

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(
            f"Retrieving HT circuits for feeder {feeder_id}: skip={skip}, limit={limit}"
        )

        try:
            stmt = (
                select(self.model)
                .where(
                    and_(
                        self.model.feeder_id == feeder_id,
                        self.model.is_deleted == False,
                    )
                )
                .offset(skip)
                .limit(limit)
                .order_by(self.model.name)
            )
            results = list(self.db_session.scalars(stmt).all())

            logger.debug(f"Retrieved {len(results)} HT circuits for feeder {feeder_id}")
            return results

        except SQLAlchemyError as e:
            logger.error(
                f"Database error retrieving HT circuits for feeder {feeder_id}: {e}"
            )
            self._handle_db_exception(e, "htcircuit")
            raise

    def get_by_pipe_id(self, pipe_id: int) -> Optional[HTCircuit]:
        """
        Get HT circuit by pipe ID.

        Args:
            pipe_id: Pipe ID

        Returns:
            Optional[HTCircuit]: The HT circuit for the pipe or None

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Searching for HT circuit for pipe {pipe_id}")

        try:
            stmt = select(self.model).where(
                and_(self.model.pipe_id == pipe_id, self.model.is_deleted == False)
            )
            result = self.db_session.scalar(stmt)

            if result:
                logger.debug(f"Found HT circuit: '{result.name}' (ID: {result.id})")
            else:
                logger.debug(f"HT circuit not found for pipe {pipe_id}")

            return result

        except SQLAlchemyError as e:
            logger.error(
                f"Database error searching for HT circuit for pipe {pipe_id}: {e}"
            )
            self._handle_db_exception(e, "htcircuit")
            raise

    def get_by_vessel_id(self, vessel_id: int) -> Optional[HTCircuit]:
        """
        Get HT circuit by vessel ID.

        Args:
            vessel_id: Vessel ID

        Returns:
            Optional[HTCircuit]: The HT circuit for the vessel or None

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Searching for HT circuit for vessel {vessel_id}")

        try:
            stmt = select(self.model).where(
                and_(self.model.vessel_id == vessel_id, self.model.is_deleted == False)
            )
            result = self.db_session.scalar(stmt)

            if result:
                logger.debug(f"Found HT circuit: '{result.name}' (ID: {result.id})")
            else:
                logger.debug(f"HT circuit not found for vessel {vessel_id}")

            return result

        except SQLAlchemyError as e:
            logger.error(
                f"Database error searching for HT circuit for vessel {vessel_id}: {e}"
            )
            self._handle_db_exception(e, "htcircuit")
            raise

    def get_with_calculations(self, feeder_id: int) -> List[HTCircuit]:
        """
        Get HT circuits with load calculations.

        Args:
            feeder_id: Feeder ID to filter by

        Returns:
            List[HTCircuit]: List of HT circuits with calculations

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Retrieving HT circuits with calculations for feeder {feeder_id}")

        try:
            stmt = (
                select(self.model)
                .where(
                    and_(
                        self.model.feeder_id == feeder_id,
                        self.model.is_deleted == False,
                        self.model.calculated_load_kw.isnot(None),
                    )
                )
                .order_by(self.model.calculated_load_kw.desc())
            )
            results = list(self.db_session.scalars(stmt).all())

            logger.debug(f"Found {len(results)} HT circuits with calculations")
            return results

        except SQLAlchemyError as e:
            logger.error(
                f"Database error retrieving HT circuits with calculations: {e}"
            )
            self._handle_db_exception(e, "htcircuit")
            raise

    def update_load_calculation(
        self,
        circuit_id: int,
        load_amps: float,
        load_kw: float,
        required_length_m: float,
    ) -> Optional[HTCircuit]:
        """
        Update HT circuit load calculation results.

        Args:
            circuit_id: ID of circuit to update
            load_amps: Calculated load in amps
            load_kw: Calculated load in kW
            required_length_m: Required cable length in meters

        Returns:
            Optional[HTCircuit]: Updated circuit or None if not found

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Updating load calculation for HT circuit {circuit_id}")

        try:
            # First check if circuit exists
            existing_circuit = self.get_by_id(circuit_id)
            if existing_circuit is None:
                logger.debug(f"HT circuit {circuit_id} not found for load update")
                return None

            # Update the circuit
            stmt = (
                update(self.model)
                .where(self.model.id == circuit_id)
                .values(
                    calculated_load_amps=load_amps,
                    calculated_load_kw=load_kw,
                    required_length_m=required_length_m,
                )
            )
            self.db_session.execute(stmt)

            # Return updated circuit
            updated_circuit = self.get_by_id(circuit_id)
            logger.debug(
                f"HT circuit {circuit_id} load calculation updated successfully"
            )
            return updated_circuit

        except SQLAlchemyError as e:
            logger.error(
                f"Database error updating HT circuit load calculation {circuit_id}: {e}"
            )
            self._handle_db_exception(e, "htcircuit")

    def get_total_feeder_load(self, feeder_id: int) -> float:
        """
        Calculate total load for a feeder.

        Args:
            feeder_id: Feeder ID

        Returns:
            float: Total load in kW

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Calculating total load for feeder {feeder_id}")

        try:
            stmt = select(func.sum(self.model.calculated_load_kw)).where(
                and_(
                    self.model.feeder_id == feeder_id,
                    self.model.is_deleted == False,
                    self.model.calculated_load_kw.isnot(None),
                )
            )
            result = self.db_session.scalar(stmt)

            total_load = result or 0.0
            logger.debug(f"Total load for feeder {feeder_id}: {total_load} kW")
            return total_load

        except SQLAlchemyError as e:
            logger.error(f"Database error calculating feeder load {feeder_id}: {e}")
            self._handle_db_exception(e, "htcircuit")
            raise


class ControlCircuitRepository(BaseRepository[ControlCircuit]):
    """
    Repository for ControlCircuit entity data access operations.

    Extends BaseRepository with control circuit-specific query methods and
    enhanced error handling for control circuit operations.
    """

    def __init__(self, db_session: Session):
        """
        Initialize the ControlCircuit repository.

        Args:
            db_session: SQLAlchemy database session
        """
        super().__init__(db_session, ControlCircuit)
        logger.debug("ControlCircuitRepository initialized")

    def get_by_switchboard_id(
        self, switchboard_id: int, skip: int = 0, limit: int = 100
    ) -> List[ControlCircuit]:
        """
        Get control circuits by switchboard ID.

        Args:
            switchboard_id: Switchboard ID to filter by
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return

        Returns:
            List[ControlCircuit]: List of control circuits on the switchboard

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(
            f"Retrieving control circuits for switchboard {switchboard_id}: skip={skip}, limit={limit}"
        )

        try:
            stmt = (
                select(self.model)
                .where(
                    and_(
                        self.model.switchboard_id == switchboard_id,
                        self.model.is_deleted == False,
                    )
                )
                .offset(skip)
                .limit(limit)
                .order_by(self.model.name)
            )
            results = list(self.db_session.scalars(stmt).all())

            logger.debug(
                f"Retrieved {len(results)} control circuits for switchboard {switchboard_id}"
            )
            return results

        except SQLAlchemyError as e:
            logger.error(
                f"Database error retrieving control circuits for switchboard {switchboard_id}: {e}"
            )
            self._handle_db_exception(e, "controlcircuit")
            raise

    def get_by_type(
        self, circuit_type: str, skip: int = 0, limit: int = 100
    ) -> List[ControlCircuit]:
        """
        Get control circuits by type.

        Args:
            circuit_type: Control circuit type to filter by
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return

        Returns:
            List[ControlCircuit]: List of control circuits of the specified type

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(
            f"Retrieving control circuits of type {circuit_type}: skip={skip}, limit={limit}"
        )

        try:
            stmt = (
                select(self.model)
                .where(
                    and_(
                        self.model.type == circuit_type, self.model.is_deleted == False
                    )
                )
                .offset(skip)
                .limit(limit)
                .order_by(self.model.name)
            )
            results = list(self.db_session.scalars(stmt).all())

            logger.debug(
                f"Retrieved {len(results)} control circuits of type {circuit_type}"
            )
            return results

        except SQLAlchemyError as e:
            logger.error(
                f"Database error retrieving control circuits of type {circuit_type}: {e}"
            )
            self._handle_db_exception(e, "controlcircuit")
            raise

    def get_with_limiting_function(
        self, switchboard_id: Optional[int] = None
    ) -> List[ControlCircuit]:
        """
        Get control circuits with limiting function enabled.

        Args:
            switchboard_id: Optional switchboard ID to filter by

        Returns:
            List[ControlCircuit]: List of control circuits with limiting function

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(
            f"Retrieving control circuits with limiting function for switchboard {switchboard_id}"
        )

        try:
            conditions = [
                self.model.has_limiting_function == True,
                self.model.is_deleted == False,
            ]

            if switchboard_id is not None:
                conditions.append(self.model.switchboard_id == switchboard_id)

            stmt = select(self.model).where(and_(*conditions)).order_by(self.model.name)
            results = list(self.db_session.scalars(stmt).all())

            logger.debug(
                f"Found {len(results)} control circuits with limiting function"
            )
            return results

        except SQLAlchemyError as e:
            logger.error(
                f"Database error retrieving control circuits with limiting function: {e}"
            )
            self._handle_db_exception(e, "controlcircuit")
            raise

    def count_by_switchboard(self, switchboard_id: int) -> int:
        """
        Count total number of active control circuits on a switchboard.

        Args:
            switchboard_id: Switchboard ID

        Returns:
            int: Number of active control circuits

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Counting control circuits for switchboard {switchboard_id}")

        try:
            stmt = select(func.count(self.model.id)).where(
                and_(
                    self.model.switchboard_id == switchboard_id,
                    self.model.is_deleted == False,
                )
            )
            result = self.db_session.scalar(stmt)

            logger.debug(
                f"Total control circuits on switchboard {switchboard_id}: {result}"
            )
            return result or 0

        except SQLAlchemyError as e:
            logger.error(
                f"Database error counting control circuits for switchboard {switchboard_id}: {e}"
            )
            self._handle_db_exception(e, "controlcircuit")
            raise


# ============================================================================
# AGGREGATE REPOSITORY CLASS
# ============================================================================


class HeatTracingRepository:
    """
    Aggregate repository class that provides access to all heat tracing repositories.

    This class acts as a facade for all heat tracing-related data access operations,
    providing a single entry point for the service layer.
    """

    def __init__(self, db_session: Session):
        """
        Initialize the aggregate heat tracing repository.

        Args:
            db_session: SQLAlchemy database session
        """
        self.db_session = db_session
        self.pipes = PipeRepository(db_session)
        self.vessels = VesselRepository(db_session)
        self.ht_circuits = HTCircuitRepository(db_session)
        self.control_circuits = ControlCircuitRepository(db_session)
        logger.debug("HeatTracingRepository aggregate initialized")

    def get_project_summary(self, project_id: int) -> Dict[str, Any]:
        """
        Get summary statistics for a project's heat tracing design.

        Args:
            project_id: Project ID

        Returns:
            Dict[str, Any]: Summary statistics

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Generating heat tracing summary for project {project_id}")

        try:
            summary = {
                "project_id": project_id,
                "total_pipes": self.pipes.count_by_project(project_id),
                "total_vessels": self.vessels.count_by_project(project_id),
                "pipes_without_circuits": len(
                    self.pipes.get_pipes_without_circuits(project_id)
                ),
                "vessels_without_circuits": len(
                    self.vessels.get_vessels_without_circuits(project_id)
                ),
                "pipes_with_calculations": len(
                    self.pipes.get_with_heat_loss_calculations(project_id)
                ),
                "vessels_with_calculations": len(
                    self.vessels.get_vessels_without_circuits(project_id)
                ),  # This should be vessels with calculations
            }

            logger.debug(f"Generated summary for project {project_id}: {summary}")
            return summary

        except Exception as e:
            logger.error(f"Error generating project summary for {project_id}: {e}")
            raise

    def get_design_readiness(self, project_id: int) -> Dict[str, Any]:
        """
        Check design readiness for a project.

        Args:
            project_id: Project ID

        Returns:
            Dict[str, Any]: Design readiness status

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Checking design readiness for project {project_id}")

        try:
            summary = self.get_project_summary(project_id)

            readiness = {
                "project_id": project_id,
                "is_ready_for_circuit_assignment": (
                    summary["pipes_without_circuits"] == 0
                    and summary["vessels_without_circuits"] == 0
                ),
                "has_heat_loss_calculations": (
                    summary["pipes_with_calculations"] > 0
                    or summary["vessels_with_calculations"] > 0
                ),
                "completion_percentage": 0.0,
                "missing_items": [],
            }

            # Calculate completion percentage
            total_items = summary["total_pipes"] + summary["total_vessels"]
            if total_items > 0:
                items_with_circuits = (
                    total_items
                    - summary["pipes_without_circuits"]
                    - summary["vessels_without_circuits"]
                )
                readiness["completion_percentage"] = (
                    items_with_circuits / total_items
                ) * 100

            # Identify missing items
            if summary["pipes_without_circuits"] > 0:
                readiness["missing_items"].append(
                    f"{summary['pipes_without_circuits']} pipes need circuit assignment"
                )

            if summary["vessels_without_circuits"] > 0:
                readiness["missing_items"].append(
                    f"{summary['vessels_without_circuits']} vessels need circuit assignment"
                )

            logger.debug(
                f"Design readiness for project {project_id}: {readiness['completion_percentage']:.1f}%"
            )
            return readiness

        except Exception as e:
            logger.error(
                f"Error checking design readiness for project {project_id}: {e}"
            )
            raise
