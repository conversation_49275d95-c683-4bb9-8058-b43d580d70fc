## **State Management**

### **Server State (Data Fetching & Caching) with React Query**

* **Primary Tool:** React Query (TanStack Query) will be used for all asynchronous data operations (fetching, caching, synchronizing, and updating server data).
* **Key Benefits:**
  * **Automatic Caching:** Reduces unnecessary network requests and provides instant UI updates.
  * **Background Re-fetching:** Keeps data fresh without explicit user interaction.
  * **Optimistic Updates:** Improves perceived performance by updating the UI before the server responds.
  * **Error Handling & Retries:** Built-in mechanisms for robust data handling.
  * **Devtools:** Excellent debugging experience.
* **Usage:**
  * Use useQuery for fetching data.
  * Use useMutation for creating, updating, or deleting data.
  * Leverage QueryClientProvider at the root of the application.
  * Define clear query keys for effective caching and invalidation.

### **Client State (Local UI State) with Zustand**

* **Primary Tool:** Zustand will be used for managing global or shared UI-specific state that is not persisted on the server (e.g., modal open/close status, theme preference, temporary form data).
* **Key Benefits:**
  * **Simplicity:** Minimal boilerplate, easy to define and consume stores.
  * **Performance:** Optimized for re-renders, only components subscribed to specific state changes will re-render.
  * **Scalability:** Easy to create multiple, independent stores for different parts of the application.
* **Usage:**
  * Create small, focused Zustand stores for specific pieces of UI state.
  * Avoid putting server-fetched data into Zustand; that's React Query's domain.

### **Component State (Local Component State)**

* **Primary Tool:** React's useState and useReducer hooks.
* **Usage:** For state that is localized to a single component and doesn't need to be shared or persisted globally.
