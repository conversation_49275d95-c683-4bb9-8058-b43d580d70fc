["tests/test_repositories/test_project_repository.py::TestProjectRepository::test_create_project", "tests/test_schemas/test_component_schemas.py::TestComponentCategoryCreateSchema::test_category_name_normalization", "tests/test_schemas/test_component_schemas.py::TestComponentCategoryCreateSchema::test_category_name_validation_empty", "tests/test_schemas/test_component_schemas.py::TestComponentCategoryCreateSchema::test_category_name_validation_whitespace", "tests/test_schemas/test_component_schemas.py::TestComponentCategoryCreateSchema::test_category_with_parent", "tests/test_schemas/test_component_schemas.py::TestComponentCategoryCreateSchema::test_minimal_category_creation", "tests/test_schemas/test_component_schemas.py::TestComponentCategoryCreateSchema::test_valid_category_creation", "tests/test_schemas/test_component_schemas.py::TestComponentCategoryUpdateSchema::test_category_update_validation_same_as_create", "tests/test_schemas/test_component_schemas.py::TestComponentCategoryUpdateSchema::test_empty_category_update", "tests/test_schemas/test_component_schemas.py::TestComponentCategoryUpdateSchema::test_valid_partial_category_update", "tests/test_schemas/test_component_schemas.py::TestComponentCreateSchema::test_category_id_validation", "tests/test_schemas/test_component_schemas.py::TestComponentCreateSchema::test_minimal_component_creation", "tests/test_schemas/test_component_schemas.py::TestComponentCreateSchema::test_name_normalization", "tests/test_schemas/test_component_schemas.py::TestComponentCreateSchema::test_name_validation_empty", "tests/test_schemas/test_component_schemas.py::TestComponentCreateSchema::test_name_validation_whitespace", "tests/test_schemas/test_component_schemas.py::TestComponentCreateSchema::test_specific_data_empty_string", "tests/test_schemas/test_component_schemas.py::TestComponentCreateSchema::test_specific_data_json_validation_invalid", "tests/test_schemas/test_component_schemas.py::TestComponentCreateSchema::test_specific_data_json_validation_valid", "tests/test_schemas/test_component_schemas.py::TestComponentCreateSchema::test_specific_data_whitespace_only", "tests/test_schemas/test_component_schemas.py::TestComponentCreateSchema::test_valid_component_creation", "tests/test_schemas/test_component_schemas.py::TestComponentListResponseSchema::test_empty_list_response", "tests/test_schemas/test_component_schemas.py::TestComponentListResponseSchema::test_list_response_structure", "tests/test_schemas/test_component_schemas.py::TestComponentReadSchema::test_read_schema_includes_all_fields", "tests/test_schemas/test_component_schemas.py::TestComponentUpdateSchema::test_empty_update", "tests/test_schemas/test_component_schemas.py::TestComponentUpdateSchema::test_update_validation_same_as_create", "tests/test_schemas/test_component_schemas.py::TestComponentUpdateSchema::test_valid_partial_update", "tests/test_schemas/test_heat_tracing_schemas.py::TestControlCircuitSchemas::test_control_circuit_create_schema_valid", "tests/test_schemas/test_heat_tracing_schemas.py::TestControlCircuitSchemas::test_control_circuit_limiting_setpoint_validation", "tests/test_schemas/test_heat_tracing_schemas.py::TestDesignWorkflowSchemas::test_design_input_schema_pipe_or_vessel_validation", "tests/test_schemas/test_heat_tracing_schemas.py::TestDesignWorkflowSchemas::test_design_input_schema_valid", "tests/test_schemas/test_heat_tracing_schemas.py::TestHTCircuitSchemas::test_htcircuit_create_schema_both_pipe_and_vessel_validation", "tests/test_schemas/test_heat_tracing_schemas.py::TestHTCircuitSchemas::test_htcircuit_create_schema_pipe_or_vessel_validation", "tests/test_schemas/test_heat_tracing_schemas.py::TestHTCircuitSchemas::test_htcircuit_create_schema_valid_pipe", "tests/test_schemas/test_heat_tracing_schemas.py::TestHTCircuitSchemas::test_htcircuit_create_schema_valid_vessel", "tests/test_schemas/test_heat_tracing_schemas.py::TestHeatLossCalculationSchemas::test_heat_loss_calculation_input_fluid_temp_validation", "tests/test_schemas/test_heat_tracing_schemas.py::TestHeatLossCalculationSchemas::test_heat_loss_calculation_input_valid", "tests/test_schemas/test_heat_tracing_schemas.py::TestHeatLossCalculationSchemas::test_heat_loss_calculation_result_valid", "tests/test_schemas/test_heat_tracing_schemas.py::TestPipeSchemas::test_pipe_create_schema_diameter_validation", "tests/test_schemas/test_heat_tracing_schemas.py::TestPipeSchemas::test_pipe_create_schema_name_validation", "tests/test_schemas/test_heat_tracing_schemas.py::TestPipeSchemas::test_pipe_create_schema_valid", "tests/test_schemas/test_heat_tracing_schemas.py::TestPipeSchemas::test_pipe_read_schema_from_dict", "tests/test_schemas/test_heat_tracing_schemas.py::TestPipeSchemas::test_pipe_update_schema_partial", "tests/test_schemas/test_heat_tracing_schemas.py::TestVesselSchemas::test_vessel_create_schema_dimensions_type_validation", "tests/test_schemas/test_heat_tracing_schemas.py::TestVesselSchemas::test_vessel_create_schema_dimensions_validation", "tests/test_schemas/test_heat_tracing_schemas.py::TestVesselSchemas::test_vessel_create_schema_valid", "tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_name_validation_empty", "tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_name_validation_whitespace", "tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_project_number_normalization", "tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_project_number_validation_empty", "tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_project_number_validation_invalid_chars", "tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_temperature_bounds_validation", "tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_temperature_range_validation", "tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_valid_project_creation", "tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_voltages_json_validation_invalid_json", "tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_voltages_json_validation_negative_voltage", "tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_voltages_json_validation_not_array", "tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_voltages_json_validation_valid", "tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_wind_speed_validation", "tests/test_schemas/test_project_schemas.py::TestProjectListResponseSchema::test_empty_list_response", "tests/test_schemas/test_project_schemas.py::TestProjectListResponseSchema::test_list_response_structure", "tests/test_schemas/test_project_schemas.py::TestProjectReadSchema::test_from_orm_conversion", "tests/test_schemas/test_project_schemas.py::TestProjectReadSchema::test_read_schema_includes_all_fields", "tests/test_schemas/test_project_schemas.py::TestProjectSummarySchema::test_summary_excludes_detailed_fields", "tests/test_schemas/test_project_schemas.py::TestProjectSummarySchema::test_summary_includes_essential_fields", "tests/test_schemas/test_project_schemas.py::TestProjectUpdateSchema::test_empty_update", "tests/test_schemas/test_project_schemas.py::TestProjectUpdateSchema::test_update_validation_same_as_create", "tests/test_schemas/test_project_schemas.py::TestProjectUpdateSchema::test_valid_partial_update", "tests/test_services/test_component_service.py::TestComponentService::test_create_component_category_not_found", "tests/test_services/test_component_service.py::TestComponentService::test_create_component_success", "tests/test_services/test_project_service.py::TestProjectService::test_create_project_duplicate_name_error", "tests/test_services/test_project_service.py::TestProjectService::test_create_project_success", "tests/test_services/test_project_service.py::TestProjectService::test_create_project_validation_error", "tests/test_services/test_project_service.py::TestProjectService::test_delete_project_success", "tests/test_services/test_project_service.py::TestProjectService::test_get_project_details_by_code", "tests/test_services/test_project_service.py::TestProjectService::test_get_project_details_by_id", "tests/test_services/test_project_service.py::TestProjectService::test_get_project_details_not_found", "tests/test_services/test_project_service.py::TestProjectService::test_get_projects_list_success", "tests/test_services/test_project_service.py::TestProjectService::test_update_project_success"]