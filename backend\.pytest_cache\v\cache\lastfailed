{"tests/test_schemas/test_project_schemas.py::TestProjectReadSchema::test_from_orm_conversion": true, "tests/test_schemas/test_project_schemas.py::TestProjectReadSchema::test_read_schema_includes_all_fields": true, "tests/test_schemas/test_project_schemas.py::TestProjectSummarySchema::test_summary_includes_essential_fields": true, "tests/test_schemas/test_project_schemas.py::TestProjectSummarySchema::test_summary_excludes_detailed_fields": true, "tests/test_schemas/test_project_schemas.py::TestProjectListResponseSchema::test_list_response_structure": true, "tests/test_repositories/test_project_repository.py::TestProjectRepository::test_create_project": true, "tests/test_schemas/test_heat_tracing_schemas.py::TestHeatLossCalculationSchemas::test_heat_loss_calculation_input_fluid_temp_validation": true, "tests/test_schemas/test_heat_tracing_schemas.py::TestPipeSchemas::test_pipe_create_schema_name_validation": true, "tests/test_schemas/test_heat_tracing_schemas.py::TestHTCircuitSchemas::test_htcircuit_create_schema_both_pipe_and_vessel_validation": true}