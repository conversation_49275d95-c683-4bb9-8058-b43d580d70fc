{"backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_create_project_success": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_create_project_validation_error": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_create_project_duplicate_error": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_get_project_by_id_success": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_get_project_by_code_success": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_get_project_not_found": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_update_project_success": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_update_project_not_found": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_delete_project_success": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_delete_project_not_found": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_list_projects_success": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_list_projects_with_pagination": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_list_projects_include_deleted": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_invalid_pagination_parameters": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_create_project": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_by_id_existing": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_by_id_nonexistent": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_by_code_existing": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_by_code_nonexistent": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_by_name_existing": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_by_name_nonexistent": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_all_projects": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_all_with_pagination": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_active_projects": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_active_projects_with_pagination": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_update_project": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_update_nonexistent_project": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_soft_delete_project": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_soft_delete_nonexistent_project": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_soft_delete_already_deleted_project": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_search_projects_by_name": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_search_projects_by_project_number": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_search_projects_by_description": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_search_projects_case_insensitive": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_search_projects_excludes_deleted": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_search_projects_with_pagination": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_count_active_projects": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_count_active_projects_empty_db": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_project_with_related_data": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_project_with_related_data_nonexistent": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_unique_constraint_name": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_unique_constraint_project_number": true, "backend/tests/test_services/test_project_service.py::TestProjectService::test_create_project_success": true, "backend/tests/test_services/test_project_service.py::TestProjectService::test_create_project_validation_error": true, "backend/tests/test_services/test_project_service.py::TestProjectService::test_create_project_duplicate_name_error": true, "backend/tests/test_services/test_project_service.py::TestProjectService::test_get_project_details_by_id": true, "backend/tests/test_services/test_project_service.py::TestProjectService::test_get_project_details_by_code": true, "backend/tests/test_services/test_project_service.py::TestProjectService::test_get_project_details_not_found": true, "backend/tests/test_services/test_project_service.py::TestProjectService::test_update_project_success": true, "backend/tests/test_services/test_project_service.py::TestProjectService::test_delete_project_success": true, "backend/tests/test_services/test_project_service.py::TestProjectService::test_get_projects_list_success": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCreateSchema::test_valid_component_creation": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCreateSchema::test_minimal_component_creation": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCreateSchema::test_name_validation_empty": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCreateSchema::test_name_validation_whitespace": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCreateSchema::test_name_normalization": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCreateSchema::test_category_id_validation": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCreateSchema::test_specific_data_json_validation_valid": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCreateSchema::test_specific_data_json_validation_invalid": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCreateSchema::test_specific_data_empty_string": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCreateSchema::test_specific_data_whitespace_only": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentUpdateSchema::test_valid_partial_update": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentUpdateSchema::test_empty_update": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentUpdateSchema::test_update_validation_same_as_create": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCategoryCreateSchema::test_valid_category_creation": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCategoryCreateSchema::test_minimal_category_creation": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCategoryCreateSchema::test_category_with_parent": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCategoryCreateSchema::test_category_name_validation_empty": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCategoryCreateSchema::test_category_name_validation_whitespace": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCategoryCreateSchema::test_category_name_normalization": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCategoryUpdateSchema::test_valid_partial_category_update": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCategoryUpdateSchema::test_empty_category_update": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCategoryUpdateSchema::test_category_update_validation_same_as_create": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentReadSchema::test_read_schema_includes_all_fields": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentListResponseSchema::test_list_response_structure": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentListResponseSchema::test_empty_list_response": true, "backend/tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_create_pipe_success": true, "backend/tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_create_pipe_validation_error": true, "backend/tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_create_pipe_duplicate_error": true, "backend/tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_get_pipe_success": true, "backend/tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_get_pipe_not_found": true, "backend/tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_update_pipe_success": true, "backend/tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_delete_pipe_success": true, "backend/tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_list_pipes_success": true, "backend/tests/test_api/test_heat_tracing_routes.py::TestVesselEndpoints::test_create_vessel_success": true, "backend/tests/test_api/test_heat_tracing_routes.py::TestVesselEndpoints::test_get_vessel_success": true, "backend/tests/test_api/test_heat_tracing_routes.py::TestCalculationEndpoints::test_calculate_pipe_heat_loss_success": true, "backend/tests/test_api/test_heat_tracing_routes.py::TestCalculationEndpoints::test_validate_standards_compliance_success": true, "backend/tests/test_api/test_heat_tracing_routes.py::TestDesignWorkflowEndpoints::test_execute_design_workflow_success": true, "backend/tests/test_schemas/test_heat_tracing_schemas.py::TestHeatLossCalculationSchemas::test_heat_loss_calculation_input_valid": true, "backend/tests/test_schemas/test_heat_tracing_schemas.py::TestHeatLossCalculationSchemas::test_heat_loss_calculation_input_fluid_temp_validation": true, "backend/tests/test_schemas/test_heat_tracing_schemas.py::TestHeatLossCalculationSchemas::test_heat_loss_calculation_result_valid": true, "backend/tests/test_schemas/test_heat_tracing_schemas.py::TestPipeSchemas::test_pipe_create_schema_valid": true, "backend/tests/test_schemas/test_heat_tracing_schemas.py::TestPipeSchemas::test_pipe_create_schema_name_validation": true, "backend/tests/test_schemas/test_heat_tracing_schemas.py::TestPipeSchemas::test_pipe_create_schema_diameter_validation": true, "backend/tests/test_schemas/test_heat_tracing_schemas.py::TestPipeSchemas::test_pipe_update_schema_partial": true, "backend/tests/test_schemas/test_heat_tracing_schemas.py::TestPipeSchemas::test_pipe_read_schema_from_dict": true, "backend/tests/test_schemas/test_heat_tracing_schemas.py::TestVesselSchemas::test_vessel_create_schema_valid": true, "backend/tests/test_schemas/test_heat_tracing_schemas.py::TestVesselSchemas::test_vessel_create_schema_dimensions_validation": true, "backend/tests/test_schemas/test_heat_tracing_schemas.py::TestVesselSchemas::test_vessel_create_schema_dimensions_type_validation": true, "backend/tests/test_schemas/test_heat_tracing_schemas.py::TestHTCircuitSchemas::test_htcircuit_create_schema_valid_pipe": true, "backend/tests/test_schemas/test_heat_tracing_schemas.py::TestHTCircuitSchemas::test_htcircuit_create_schema_valid_vessel": true, "backend/tests/test_schemas/test_heat_tracing_schemas.py::TestHTCircuitSchemas::test_htcircuit_create_schema_pipe_or_vessel_validation": true, "backend/tests/test_schemas/test_heat_tracing_schemas.py::TestHTCircuitSchemas::test_htcircuit_create_schema_both_pipe_and_vessel_validation": true, "backend/tests/test_schemas/test_heat_tracing_schemas.py::TestControlCircuitSchemas::test_control_circuit_create_schema_valid": true, "backend/tests/test_schemas/test_heat_tracing_schemas.py::TestControlCircuitSchemas::test_control_circuit_limiting_setpoint_validation": true, "backend/tests/test_schemas/test_heat_tracing_schemas.py::TestDesignWorkflowSchemas::test_design_input_schema_valid": true, "backend/tests/test_schemas/test_heat_tracing_schemas.py::TestDesignWorkflowSchemas::test_design_input_schema_pipe_or_vessel_validation": true, "backend/tests/test_services/test_heat_tracing_service.py::TestHeatTracingServicePipeOperations::test_create_pipe_success": true, "backend/tests/test_services/test_heat_tracing_service.py::TestHeatTracingServicePipeOperations::test_create_pipe_validation_error": true, "backend/tests/test_services/test_heat_tracing_service.py::TestHeatTracingServicePipeOperations::test_create_pipe_duplicate_error": true, "backend/tests/test_services/test_heat_tracing_service.py::TestHeatTracingServicePipeOperations::test_get_pipe_details_success": true, "backend/tests/test_services/test_heat_tracing_service.py::TestHeatTracingServicePipeOperations::test_get_pipe_details_not_found": true, "backend/tests/test_services/test_heat_tracing_service.py::TestHeatTracingServicePipeOperations::test_get_pipe_details_deleted": true, "backend/tests/test_services/test_heat_tracing_service.py::TestHeatTracingServicePipeOperations::test_update_pipe_success": true, "backend/tests/test_services/test_heat_tracing_service.py::TestHeatTracingServicePipeOperations::test_update_pipe_not_found": true, "backend/tests/test_services/test_heat_tracing_service.py::TestHeatTracingServicePipeOperations::test_delete_pipe_success": true, "backend/tests/test_services/test_heat_tracing_service.py::TestHeatTracingServicePipeOperations::test_get_pipes_list_success": true, "backend/tests/test_services/test_heat_tracing_service.py::TestHeatTracingServiceVesselOperations::test_create_vessel_success": true, "backend/tests/test_services/test_heat_tracing_service.py::TestHeatTracingServiceVesselOperations::test_create_vessel_invalid_dimensions": true, "backend/tests/test_services/test_heat_tracing_service.py::TestHeatTracingServiceVesselOperations::test_get_vessel_details_success": true, "backend/tests/test_services/test_heat_tracing_service.py::TestHeatTracingServiceCalculations::test_calculate_pipe_heat_loss_success": true, "backend/tests/test_services/test_heat_tracing_service.py::TestHeatTracingServiceCalculations::test_calculate_pipe_heat_loss_pipe_not_found": true, "backend/tests/test_services/test_heat_tracing_service.py::TestHeatTracingServiceCalculations::test_validate_standards_compliance_success": true, "backend/tests/test_services/test_heat_tracing_service.py::TestHeatTracingServiceDesignWorkflow::test_execute_design_workflow_success": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestPipeRepository::test_create_pipe_success": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestPipeRepository::test_get_pipe_by_id_success": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestPipeRepository::test_get_pipe_by_id_not_found": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestPipeRepository::test_get_pipes_by_project_id": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestPipeRepository::test_get_pipe_by_line_tag": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestPipeRepository::test_get_pipe_by_line_tag_not_found": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestPipeRepository::test_get_pipes_without_circuits": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestPipeRepository::test_update_heat_loss_calculation": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestPipeRepository::test_update_heat_loss_calculation_not_found": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestPipeRepository::test_count_by_project": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestPipeRepository::test_get_with_heat_loss_calculations": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestVesselRepository::test_create_vessel_success": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestVesselRepository::test_get_vessel_by_equipment_tag": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestVesselRepository::test_get_vessels_without_circuits": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestVesselRepository::test_update_heat_loss_calculation": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestHTCircuitRepository::test_create_htcircuit_success": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestHTCircuitRepository::test_get_by_feeder_id": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestHTCircuitRepository::test_get_by_pipe_id": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestHTCircuitRepository::test_update_load_calculation": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestHTCircuitRepository::test_get_total_feeder_load": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestControlCircuitRepository::test_create_control_circuit_success": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestControlCircuitRepository::test_get_by_switchboard_id": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestControlCircuitRepository::test_get_with_limiting_function": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestHeatTracingRepository::test_initialization": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestHeatTracingRepository::test_get_project_summary_empty": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestHeatTracingRepository::test_get_design_readiness_empty": true}